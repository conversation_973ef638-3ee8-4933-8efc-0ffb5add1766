
<?php
require_once 'secrets.php';
require_once 'config.php';
require_once 'clientmethods/PriceService.php';
require_once 'n8n_helper_functions.php';

header('Content-Type: application/json');

// N8N Secret key kontrolü (Stripe benzeri)
$n8nSecretKey = $n8nWebhookSecret ?? 'your-n8n-secret-key';
$receivedKey = $_SERVER['HTTP_X_N8N_SECRET'] ?? '';

if ($receivedKey !== $n8nSecretKey) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized - Invalid N8N secret']);
    exit;
}

// N8N webhook handler
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);

    $action = $input['action'] ?? '';
    $data = $input['data'] ?? [];

    if ($action === 'create_portfolio_template') {
        $result = handleCreatePortfolioTemplate($data);
        echo json_encode($result);
    } elseif ($action === 'save_portfolio_template') {
        $result = handleSavePortfolioTemplate($data);
        echo json_encode($result);
    } else {
        $result = [
            'status' => 'error',
            'message' => 'Unknown action: ' . $action,
            'received_action' => $action
        ];
        echo json_encode($result);
    }
} else {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
}

function handleCreatePortfolioTemplate($data) {
    try {
        require_once 'clientmethods/PortfolioScore.php';
        require_once 'clientmethods/CoinDetailMethods.php';
        require_once 'n8n_helper_functions.php';
        require_once 'config.php';
        require_once 'utils.php';

        error_log("Portfolio template received: " . json_encode($data));

        // N8N'den gelen veriyi portfolio context formatına çevir
        $portfolioContext = convertN8NDataToPortfolioContext($data);

        if (!$portfolioContext) {
            return [
                'status' => 'error',
                'message' => 'Invalid portfolio data format',
                'required_fields' => getRequiredN8NFields()
            ];
        }

        error_log("Portfolio context created: " . json_encode($portfolioContext));
        // Portfolio skorlarını hesapla
        $metrics = build_portfolio_metrics($portfolioContext);
        error_log("Portfolio metrics calculated: " . json_encode($metrics));

        // Artık create sadece analiz ve hesaplar döner; DB'ye yazma ayrı hook'ta
        return [
            'status' => 'success',
            'message' => 'Portfolio template analyzed successfully',
            'template_name' => $data['template_name'] ?? 'N8N Portfolio',
            'metrics' => $metrics,
            'portfolio' => $portfolioContext,
            'portfolio_summary' => [
                'total_assets' => count($portfolioContext['assets']),
                'total_value' => $portfolioContext['totalValue']
            ]
        ];

    } catch (Exception $e) {
        error_log("Portfolio template error: " . $e->getMessage());
        return [
            'status' => 'error',
            'message' => 'Failed to process portfolio template',
            'error' => $e->getMessage()
        ];
    }
}

function handleSavePortfolioTemplate($data) {
    try {
        require_once 'config.php';
        global $link;

        // Sade kayıt: sadece template_name ve (coin_id, amount)
        $templateName = isset($data['template_name']) ? trim((string)$data['template_name']) : '';
        $assetsInput = $data['assets'] ?? [];
        if ($templateName === '' || !is_array($assetsInput) || count($assetsInput) === 0) {
            return [
                'status' => 'error',
                'message' => 'Invalid input: template_name and assets are required'
            ];
        }

        // Insert template
        $stmt = mysqli_prepare($link, "INSERT INTO portfolio_templates (template_name) VALUES (?)");
        if (!$stmt) {
            return [ 'status' => 'error', 'message' => 'DB error (prepare template)'];
        }
        mysqli_stmt_bind_param($stmt, 's', $templateName);
        if (!mysqli_stmt_execute($stmt)) {
            mysqli_stmt_close($stmt);
            return [ 'status' => 'error', 'message' => 'DB error (execute template)'];
        }
        $templateId = mysqli_insert_id($link);
        mysqli_stmt_close($stmt);

        // Insert assets: coin_id, amount (allocation'dan amount hesapla gerekirse)
        $stmtA = mysqli_prepare($link, "INSERT INTO portfolio_template_assets (template_id, coin_id, amount) VALUES (?, ?, ?)");
        if ($stmtA) {
            foreach ($assetsInput as $a) {
                // coin_id zorunlu, amount veya allocation'dan biri olmalı
                if (!isset($a['coin_id']) || (!isset($a['amount']) && !isset($a['allocation']))) {
                    continue;
                }

                $coinId = (int)$a['coin_id'];

                // Eğer amount varsa direkt kullan
                if (isset($a['amount'])) {
                    $amount = (float)$a['amount'];
                } else {
                    // allocation varsa, varsayılan bir değer kullanarak amount hesapla
                    // Template'ler için genellikle yüzde oranları kullanılır
                    // Bu durumda allocation'ı amount olarak kaydet (template için uygun)
                    $amount = (float)$a['allocation'];
                }

                mysqli_stmt_bind_param($stmtA, 'iid', $templateId, $coinId, $amount);
                mysqli_stmt_execute($stmtA);
            }
            mysqli_stmt_close($stmtA);
        }

        return [
            'status' => 'success',
            'message' => 'Portfolio template saved successfully',
            'template_id' => (int)$templateId,
            'template_name' => $templateName
        ];

    } catch (Exception $e) {
        error_log("Portfolio template save error: " . $e->getMessage());
        return [
            'status' => 'error',
            'message' => 'Failed to save portfolio template',
            'error' => $e->getMessage()
        ];
    }
}


function getRequiredN8NFields() {
    return [
        'template_name' => 'string - Portfolio template name',
        'assets' => 'array - List of portfolio assets',
        'assets[].coin_id' => 'int - Coin database ID (coindata tablosundan)',
        'assets[].allocation' => 'float - Asset allocation percentage (0-100) - for create_portfolio_template',
        'assets[].amount' => 'float - Asset amount - for save_portfolio_template (alternative to allocation)',
        // Opsiyonel alanlar - yoksa veritabanından çekilir
        'assets[].symbol' => 'string - Coin symbol (optional, DB den çekilir)',
        'assets[].name' => 'string - Coin name (optional, DB den çekilir)',
        'assets[].market_cap' => 'float - Market cap (optional, DB den çekilir)',
        'assets[].ai_score' => 'float - AI score (optional, DB den çekilir)',
        'assets[].is_vetted' => 'bool - Vetted status (optional, DB den çekilir)',
        'assets[].categories' => 'array - Categories (optional, DB den çekilir)'
    ];
}

function convertN8NDataToPortfolioContext($data) {

    if (!isset($data['assets']) || !is_array($data['assets'])) {
        return null;
    }

    $totalValue = 1000000; // 1M USD varsayılan portföy değeri
    $assets = [];

    // Önce hangi veri tipini kullandığımızı belirle
    $useAllocation = false;
    $useAmount = false;

    foreach ($data['assets'] as $asset) {
        if (isset($asset['allocation'])) {
            $useAllocation = true;
            break;
        }
        if (isset($asset['amount'])) {
            $useAmount = true;
            break;
        }
    }

    // Eğer amount kullanıyorsak, önce toplam amount'ı hesapla
    $totalAmount = 0;
    if ($useAmount && !$useAllocation) {
        foreach ($data['assets'] as $asset) {
            if (isset($asset['amount']) && isset($asset['coin_id'])) {
                $totalAmount += (float)$asset['amount'];
            }
        }
    }

    foreach ($data['assets'] as $asset) {
        // coin_id zorunlu
        if (!isset($asset['coin_id'])) {
            continue;
        }

        $coinId = (int)$asset['coin_id'];

        // allocation veya amount'tan allocation hesapla
        if ($useAllocation && isset($asset['allocation'])) {
            $allocation = (float)$asset['allocation'];
            $assetValue = ($allocation / 100) * $totalValue;
        } elseif ($useAmount && isset($asset['amount']) && $totalAmount > 0) {
            $amount = (float)$asset['amount'];
            $allocation = ($amount / $totalAmount) * 100;
            $assetValue = ($allocation / 100) * $totalValue;
        } else {
            continue; // Geçersiz veri
        }

        // Veritabanından coin bilgilerini çek
        $coinData = fetchCoinDataFromDB($coinId);
        if (!$coinData) {
            continue; // Geçersiz coin_id
        }

        // Fiyatı id ile çek (geckoslug=false)
        $currentPrice = get_current_price_by_coin_id($coinId, ['isGeckoSlug' => false]);
        if (!$currentPrice || $currentPrice <= 0) {
            // Yedek: DB helper'ın döndürdüğü current_price varsa onu kullan
            $currentPrice = $coinData['current_price'] ?? null;
        }

        // Eğer amount kullanıyorsak, gelen amount'ı kullan; allocation kullanıyorsak hesapla
        if ($useAmount && isset($asset['amount'])) {
            $calculatedAmount = (float)$asset['amount'];
        } else {
            $calculatedAmount = $currentPrice && $currentPrice > 0 ? ($assetValue / $currentPrice) : 0;
        }

        $assets[] = [
            'coinId' => $coinId,
            'symbol' => $asset['symbol'] ?? $coinData['symbol'],
            'name' => $asset['name'] ?? $coinData['name'],
            'allocation' => $allocation,
            'currentPrice' => $currentPrice,
            'currentValue' => $assetValue,
            'totalInvested' => $assetValue,
            'amount' => $calculatedAmount,
            'marketCap' => (float)($asset['market_cap'] ?? $coinData['marketcap'] ?? 0),
            'aiScore' => (float)($asset['ai_score'] ?? $coinData['total_score'] ?? 50),
            'isVetted' => (bool)($asset['is_vetted'] ?? $coinData['is_vetted'] ?? false),
            'categories' => $asset['categories'] ?? $coinData['categories'] ?? [],
            'fdv' => $coinData['fdv'] ?? null,
            'volume24h' => $coinData['total_volume'] ?? null,
            'change24h' => $coinData['price_change_1d'] ?? null,
            'change7d' => $coinData['price_change_7d'] ?? null
        ];
    }

    return [
        'assets' => $assets,
        'totalValue' => $totalValue,
        'assetCount' => count($assets)
    ];
}
?>
